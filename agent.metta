;; ------------------------
;; Load knowledge & rules
;; ------------------------
!(load-file "knowledge.mett")
!(load-file "rules.mett")

;; ------------------------
;; Backward Chainer
;; ------------------------

(: bc_suggest (-> <PERSON>om <PERSON>))

;; Base case
(= (bc_suggest $goal Z)
   (match &kb $goal $goal))

;; Recursive case
(= (bc_suggest $goal (S $k))
   (match &rb (: $_ (-> $pre $goal))
     (let (($p (bc_suggest $pre $k)))
       $goal)))

;; ------------------------
;; Run agent and display result
;; ------------------------

!(bind! chili-result (bc_suggest (is_food chili) (fromNumber 3)))
!(bind! curry-result (bc_suggest (is_food curry) (fromNumber 3)))
!(bind! salad-result (bc_suggest (is_food salad) (fromNumber 3)))
!(bind! fried_rice-result (bc_suggest (is_food fried_rice) (fromNumber 3)))

(if chili-result
  !(println! "Recommend: chili"))

(if curry-result
  !(println! "Recommend: curry"))

(if salad-result
  !(println! "Recommend: salad"))

(if fried_rice-result
  !(println! "Recommend: fried_rice"))
